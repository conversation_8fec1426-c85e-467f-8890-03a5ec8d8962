name: Deploy Salmate Monitoring

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: "Force deployment even if no changes detected"
        required: false
        default: false
        type: boolean

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Test SSH connection
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "echo 'SSH connection successful'"

      - name: Create deployment directory
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p ~/salmate-monitoring"

      - name: Backup existing data directories
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            # Create backup directory with timestamp
            BACKUP_DIR=".backup_$(date +%Y%m%d_%H%M%S)"
            mkdir -p "$BACKUP_DIR"

            # Backup critical data directories if they exist
            if [ -d "traefik/cert" ]; then
              echo "Backing up Traefik certificates..."
              sudo cp -rp traefik/cert "$BACKUP_DIR/traefik_cert"
            fi

            if [ -d "grafana/data" ]; then
              echo "Backing up Grafana data..."
              sudo cp -rp grafana/data "$BACKUP_DIR/grafana_data"
            fi

            if [ -d "prometheus/data" ]; then
              echo "Backing up Prometheus data..."
              sudo cp -rp prometheus/data "$BACKUP_DIR/prometheus_data"
            fi

            if [ -d "alertmanager/data" ]; then
              echo "Backing up Alertmanager data..."
              sudo cp -rp alertmanager/data "$BACKUP_DIR/alertmanager_data"
            fi

            echo "Backup completed in $BACKUP_DIR"
          '

      - name: Stop existing services
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring
            if [ -f "docker-compose.yml" ]; then
              echo "Stopping existing services..."
              docker compose down || docker-compose down || true
            fi
          '

      - name: Deploy application files
        run: |
          # Use rsync for more reliable deployment
          # First, create a clean temporary directory
          mkdir -p /tmp/salmate-deploy

          # Copy files to temporary directory, excluding problematic files
          sudo cp -rp . /tmp/salmate-deploy/ 2>/dev/null || true

          # Remove unwanted files from the temporary directory
          rm -rf /tmp/salmate-deploy/.git
          rm -rf /tmp/salmate-deploy/.github
          rm -f /tmp/salmate-deploy/*.tar.gz
          rm -rf /tmp/salmate-deploy/node_modules
          rm -f /tmp/salmate-deploy/.DS_Store
          rm -f /tmp/salmate-deploy/*.log

          # Create archive from clean directory
          cd /tmp/salmate-deploy
          tar -czf ../salmate-monitoring.tar.gz .
          cd -

          # Copy archive to server
          scp -i ~/.ssh/id_rsa -P ${{ secrets.SSH_PORT }} /tmp/salmate-monitoring.tar.gz ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/

          # Extract and deploy on server
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            # Remove old application files (but keep data directories and backups)
            find . -maxdepth 1 -type f \
              -not -name ".backup_*" \
              -not -name ".env" \
              -delete 2>/dev/null || true

            # Remove directories except data directories and backups
            for dir in */; do
              if [[ "$dir" != "traefik/" && "$dir" != "grafana/" && "$dir" != "prometheus/" && "$dir" != "alertmanager/" && "$dir" != .backup_*/ ]]; then
                rm -rf "$dir" 2>/dev/null || true
              fi
            done

            # Extract new files
            cd ~/salmate-monitoring
            tar -xzf ~/salmate-monitoring.tar.gz
            rm ~/salmate-monitoring.tar.gz

            # Set proper permissions
            cd ~/salmate-monitoring
            sudo chown -R nobody:nogroup grafana/data
            sudo chown -R nobody:nogroup prometheus/data
            sudo chown -R nobody:nogroup alertmanager/data
            sudo chmod -R 777 grafana/data
            sudo chmod -R 777 prometheus/data
            sudo chmod -R 777 alertmanager/data

            echo "Application files deployed successfully"
          '

          # Cleanup temporary files
          rm -rf /tmp/salmate-deploy /tmp/salmate-monitoring.tar.gz

      - name: Restore preserved data directories
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            # Find the most recent backup directory
            LATEST_BACKUP=$(ls -1d .backup_* 2>/dev/null | tail -1)

            if [ -n "$LATEST_BACKUP" ] && [ -d "$LATEST_BACKUP" ]; then
              echo "Restoring data from $LATEST_BACKUP..."

              # Restore Traefik certificates
              if [ -d "$LATEST_BACKUP/traefik_cert" ]; then
                echo "Restoring Traefik certificates..."
                mkdir -p traefik/cert
                sudo cp -rp "$LATEST_BACKUP/traefik_cert"/* traefik/cert/ 2>/dev/null || true
              fi

              # Restore Grafana data
              if [ -d "$LATEST_BACKUP/grafana_data" ]; then
                echo "Restoring Grafana data..."
                mkdir -p grafana/data
                sudo cp -rp "$LATEST_BACKUP/grafana_data"/* grafana/data/ 2>/dev/null || true
                sudo chown -R nobody:nogroup grafana/data
                sudo chmod -R 777 grafana/data
              fi

              # Restore Prometheus data
              if [ -d "$LATEST_BACKUP/prometheus_data" ]; then
                echo "Restoring Prometheus data..."
                mkdir -p prometheus/data
                sudo cp -rp "$LATEST_BACKUP/prometheus_data"/* prometheus/data/ 2>/dev/null || true
                sudo chown -R nobody:nogroup prometheus/data
                sudo chmod -R 777 prometheus/data
              fi

              # Restore Alertmanager data
              if [ -d "$LATEST_BACKUP/alertmanager_data" ]; then
                echo "Restoring Alertmanager data..."
                mkdir -p alertmanager/data
                sudo cp -rp "$LATEST_BACKUP/alertmanager_data"/* alertmanager/data/ 2>/dev/null || true
              fi

              echo "Data restoration completed"
            else
              echo "No backup found, proceeding with fresh installation"
            fi
          '

      - name: Validate deployment
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            echo "Validating deployment..."

            # Check critical files exist
            if [ ! -f "docker-compose.yml" ]; then
              echo "ERROR: docker-compose.yml not found"
              exit 1
            fi

            if [ ! -f ".env" ]; then
              echo "WARNING: .env file not found. Please ensure environment variables are configured."
            fi

            # Check Docker is available
            if ! command -v docker &> /dev/null; then
              echo "ERROR: Docker is not installed"
              exit 1
            fi

            # Check Docker Compose is available
            if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
              echo "ERROR: Docker Compose is not available"
              exit 1
            fi

            echo "Deployment validation completed successfully"
          '

      - name: Start services
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            echo "Starting monitoring services..."

            # Use docker compose (newer) or docker-compose (legacy)
            if docker compose version &> /dev/null; then
              docker compose up -d
            else
              docker-compose up -d
            fi

            echo "Services started successfully"
          '

      - name: Health check
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            echo "Performing health checks..."

            # Wait for services to start
            sleep 30

            # Check if containers are running
            TRAEFIK_RUNNING=$(docker ps --filter "name=traefik" --filter "status=running" -q | wc -l)
            PROMETHEUS_RUNNING=$(docker ps --filter "name=prometheus" --filter "status=running" -q | wc -l)
            GRAFANA_RUNNING=$(docker ps --filter "name=grafana" --filter "status=running" -q | wc -l)
            BLACKBOX_RUNNING=$(docker ps --filter "name=blackbox" --filter "status=running" -q | wc -l)

            echo "Service status:"
            echo "- Traefik: $TRAEFIK_RUNNING container(s) running"
            echo "- Prometheus: $PROMETHEUS_RUNNING container(s) running"
            echo "- Grafana: $GRAFANA_RUNNING container(s) running"
            echo "- Blackbox Exporter: $BLACKBOX_RUNNING container(s) running"

            # Check if critical services are running
            if [ "$TRAEFIK_RUNNING" -eq 0 ]; then
              echo "ERROR: Traefik is not running"
              docker logs traefik || true
              exit 1
            fi

            if [ "$PROMETHEUS_RUNNING" -eq 0 ]; then
              echo "ERROR: Prometheus is not running"
              docker logs prometheus || true
              exit 1
            fi

            if [ "$GRAFANA_RUNNING" -eq 0 ]; then
              echo "ERROR: Grafana is not running"
              docker logs grafana || true
              exit 1
            fi

            echo "All critical services are running successfully"
          '

      - name: Setup ctop alias
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            echo "alias ctop='docker run --rm -ti --name=ctop -v /var/run/docker.sock:/var/run/docker.sock:ro quay.io/vektorlab/ctop:latest'" > ~/.bash_aliases
          '

      - name: Cleanup old backups
        run: |
          ssh -i ~/.ssh/id_rsa -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} '
            cd ~/salmate-monitoring

            # Keep only the 5 most recent backups
            BACKUP_COUNT=$(ls -1d backup_* 2>/dev/null | wc -l)
            if [ "$BACKUP_COUNT" -gt 5 ]; then
              echo "Cleaning up old backups (keeping 5 most recent)..."
              ls -1td backup_* | tail -n +6 | xargs rm -rf
              echo "Old backups cleaned up"
            fi
          '

      - name: Cleanup SSH key
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa

      - name: Deployment summary
        run: |
          echo "🎉 Deployment completed successfully!"
          echo ""
          echo "📊 Monitoring system is now available at:"
          echo "- Traefik Dashboard: https://\$DOMAIN_NAME/traefik/"
          echo "- Prometheus: https://\$DOMAIN_NAME/prometheus/"
          echo "- Grafana: https://\$DOMAIN_NAME/grafana/"
          echo ""
          echo "⚠️  Note: Replace \$DOMAIN_NAME with your actual domain name"
          echo "⚠️  SSL certificates may take a few minutes to be issued by Let's Encrypt"
