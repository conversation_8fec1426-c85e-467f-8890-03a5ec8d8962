{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 3, "links": [], "panels": [{"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 0, "y": 0}, "id": 37, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center; font-size: 30px; font-weight: bold;\">\n  Staging\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 6, "y": 0}, "id": 38, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center; font-size: 30px; font-weight: bold;\">\n  TPB\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 12, "y": 0}, "id": 39, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center; font-size: 30px; font-weight: bold;\">\n  Viriyah\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 18, "y": 0}, "id": 40, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center; font-size: 30px; font-weight: bold;\">\n  Sila (dev)\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 0, "y": 2}, "id": 69, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center;\">\n  <a href=\"https://www.salmate-staging.aibrainlab.co\" target=\"_blank\">🌐 salmate-staging.aibrainlab.co</a>\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 6, "y": 2}, "id": 70, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center;\">\n  <a href=\"https://www.tpb.aibrainlab.co\" target=\"_blank\">🌐 tpb.aibrainlab.co</a>\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 12, "y": 2}, "id": 71, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center;\">\n  <a href=\"https://www.viriyah.aibrainlab.co\" target=\"_blank\">🌐 viriyah.aibrainlab.co</a>\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 6, "x": 18, "y": 2}, "id": 72, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<p style=\"text-align: center;\">\n  <a href=\"https://www.sila-dev.aibrainlab.co\" target=\"_blank\">🌐 sila-dev.aibrainlab.co</a>\n</p>", "mode": "html"}, "pluginVersion": "12.0.0", "title": "", "transparent": true, "type": "text"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 10, "panels": [], "title": "Core System Health", "type": "row"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "yellow", "value": 60}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 5}, "id": 9, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": false, "sizing": "auto", "text": {}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "100 - (avg by (environment) (rate(node_cpu_seconds_total{environment=\"staging\", mode=\"idle\"}[30s])) * 100)", "format": "time_series", "hide": false, "instant": false, "legendFormat": "% CPU Usage", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "(node_memory_MemTotal_bytes{environment=\"staging\"} - node_memory_MemAvailable_bytes{environment=\"staging\"}) / node_memory_MemTotal_bytes{environment=\"staging\"} * 100", "hide": false, "instant": false, "legendFormat": "% Memory Usage", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "  100 - (node_filesystem_avail_bytes{environment=\"staging\", mountpoint=\"/\"} * 100 / node_filesystem_size_bytes{environment=\"staging\", mountpoint=\"/\"})", "legendFormat": "% Disk Usage", "range": true, "refId": "C"}], "title": "CPU / Memory / Disk", "type": "gauge"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "yellow", "value": 60}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 5}, "id": 11, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": false, "sizing": "auto", "text": {}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "100 - (avg by (environment) (rate(node_cpu_seconds_total{environment=\"tpb\", mode=\"idle\"}[30s])) * 100)", "format": "time_series", "hide": false, "instant": false, "legendFormat": "% CPU Usage", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "(node_memory_MemTotal_bytes{environment=\"tpb\"} - node_memory_MemAvailable_bytes{environment=\"tpb\"}) / node_memory_MemTotal_bytes{environment=\"tpb\"} * 100", "hide": false, "instant": false, "legendFormat": "% Memory Usage", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "  100 - (node_filesystem_avail_bytes{environment=\"tpb\", mountpoint=\"/\"} * 100 / node_filesystem_size_bytes{environment=\"tpb\", mountpoint=\"/\"})", "legendFormat": "% Disk Usage", "range": true, "refId": "C"}], "title": "CPU / Memory / Disk", "type": "gauge"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "yellow", "value": 60}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 5}, "id": 13, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": false, "sizing": "auto", "text": {}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "100 - (avg by (environment) (rate(node_cpu_seconds_total{environment=\"viriyah\", mode=\"idle\"}[30s])) * 100)", "format": "time_series", "hide": false, "instant": false, "legendFormat": "% CPU Usage", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "(node_memory_MemTotal_bytes{environment=\"viriyah\"} - node_memory_MemAvailable_bytes{environment=\"viriyah\"}) / node_memory_MemTotal_bytes{environment=\"viriyah\"} * 100", "hide": false, "instant": false, "legendFormat": "% Memory Usage", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "  100 - (node_filesystem_avail_bytes{environment=\"viriyah\", mountpoint=\"/\"} * 100 / node_filesystem_size_bytes{environment=\"viriyah\", mountpoint=\"/\"})", "legendFormat": "% Disk Usage", "range": true, "refId": "C"}], "title": "CPU / Memory / Disk", "type": "gauge"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "yellow", "value": 60}, {"color": "red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 5}, "id": 12, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": false, "sizing": "auto", "text": {}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "100 - (avg by (environment) (rate(node_cpu_seconds_total{environment=\"sila\", mode=\"idle\"}[30s])) * 100)", "format": "time_series", "hide": false, "instant": false, "legendFormat": "% CPU Usage", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "(node_memory_MemTotal_bytes{environment=\"sila\"} - node_memory_MemAvailable_bytes{environment=\"sila\"}) / node_memory_MemTotal_bytes{environment=\"sila\"} * 100", "hide": false, "instant": false, "legendFormat": "% Memory Usage", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "  100 - (node_filesystem_avail_bytes{environment=\"sila\", mountpoint=\"/\"} * 100 / node_filesystem_size_bytes{environment=\"sila\", mountpoint=\"/\"})", "legendFormat": "% Disk Usage", "range": true, "refId": "C"}], "title": "CPU / Memory / Disk", "type": "gauge"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 7, "panels": [], "title": "Container Health", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisPlacement": "auto", "fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [{"options": {"0": {"index": 0, "text": "DOWN"}, "1": {"index": 1, "text": "UP"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 11}, "id": 6, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": false}, "mergeValues": true, "rowHeight": 0.6, "showValue": "never", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "label_replace(up{environment=\"staging\", job!~\"prometheus|blackbox|cadvisor|node-exporter|federate\"}, \"instance\", \"$1\", \"instance\", \"(.*):[0-9]+\")", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(redis_up{environment=\"staging\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(pg_up{environment=\"staging\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "C"}], "title": "", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["instance"], "mode": "columns", "valueLabel": "instance"}}], "type": "state-timeline"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisPlacement": "auto", "fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [{"options": {"0": {"index": 0, "text": "DOWN"}, "1": {"index": 1, "text": "UP"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 11}, "id": 4, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": false}, "mergeValues": true, "rowHeight": 0.6, "showValue": "never", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "label_replace(up{environment=\"tpb\", job!~\"prometheus|blackbox|cadvisor|node-exporter|federate\"}, \"instance\", \"$1\", \"instance\", \"(.*):[0-9]+\")", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(redis_up{environment=\"tpb\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(pg_up{environment=\"tpb\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "C"}], "title": "", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["instance"], "mode": "columns", "valueLabel": "instance"}}], "type": "state-timeline"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisPlacement": "auto", "fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [{"options": {"0": {"index": 0, "text": "DOWN"}, "1": {"index": 1, "text": "UP"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 11}, "id": 3, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": false}, "mergeValues": true, "rowHeight": 0.6, "showValue": "never", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "label_replace(up{environment=\"viriyah\", job!~\"prometheus|blackbox|cadvisor|node-exporter|federate\"}, \"instance\", \"$1\", \"instance\", \"(.*):[0-9]+\")", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(redis_up{environment=\"viriyah\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(pg_up{environment=\"viriyah\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "C"}], "title": "", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["instance"], "mode": "columns", "valueLabel": "instance"}}], "type": "state-timeline"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisPlacement": "auto", "fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [{"options": {"0": {"index": 0, "text": "DOWN"}, "1": {"index": 1, "text": "UP"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 11}, "id": 5, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": false}, "mergeValues": true, "rowHeight": 0.6, "showValue": "never", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "label_replace(up{environment=\"sila\", job!~\"prometheus|blackbox|cadvisor|node-exporter|federate\"}, \"instance\", \"$1\", \"instance\", \"(.*):[0-9]+\")", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(redis_up{environment=\"sila\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "label_replace(pg_up{environment=\"sila\"}, \"instance\", \"$1\", \"job\", \"(.*)\")", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "C"}], "title": "", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["instance"], "mode": "columns", "valueLabel": "instance"}}], "type": "state-timeline"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 0, "y": 19}, "id": 52, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"staging\", name=\"salmate-frontend-frontend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FRONTEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 1, "y": 19}, "id": 51, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"staging\", name=\"salmate-backend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "BACKEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 2, "y": 19}, "id": 54, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"staging\", name=\"information-faq-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FAQ", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 3, "y": 19}, "id": 53, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"staging\", name=\"intent-intend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "INTENT", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 4, "y": 19}, "id": 55, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"staging\", name=\"langgraph-langgraph-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "LANGGRAPH", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 5, "y": 19}, "id": 56, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"staging\", name=\"vectordb-vectordb-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "TOOL", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 6, "y": 19}, "id": 58, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"tpb\", name=\"frontend-production\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FRONTEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 7, "y": 19}, "id": 57, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"tpb\", name=\"backend-production\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "BACKEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 8, "y": 19}, "id": 59, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"tpb\", name=\"information-faq-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FAQ", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 9, "y": 19}, "id": 61, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"tpb\", name=\"intent-intent-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "INTENT", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 10, "y": 19}, "id": 60, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"tpb\", name=\"langgraph-langgraph-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "LANGGRAPH", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 11, "y": 19}, "id": 62, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"tpb\", name=\"tool-tool-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "TOOL", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 12, "y": 19}, "id": 63, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"viriyah\", name=\"frontend-frontend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FRONTEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 13, "y": 19}, "id": 64, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"viriyah\", name=\"backend-backend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "BACKEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 14, "y": 19}, "id": 65, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"viriyah\", name=\"information-faq-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FAQ", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 15, "y": 19}, "id": 66, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"viriyah\", name=\"intent-intent-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "INTENT", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 16, "y": 19}, "id": 67, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"viriyah\", name=\"langgraph-langgraph-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "LANGGRAPH", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 17, "y": 19}, "id": 68, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"viriyah\", name=\"tool-tool-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "TOOL", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 18, "y": 19}, "id": 45, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"sila\", name=\"frontend-frontend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FRONTEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 19, "y": 19}, "id": 46, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"sila\", name=\"backend-backend-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "BACKEND", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 20, "y": 19}, "id": 47, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"sila\", name=\"information-faq-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "FAQ", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 21, "y": 19}, "id": 48, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"sila\", name=\"intent-intent-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "INTENT", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 22, "y": 19}, "id": 49, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"sila\", name=\"langgraph-langgraph-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "LANGGRAPH", "type": "stat"}, {"datasource": {"uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "text"}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 1, "x": 23, "y": 19}, "id": 50, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "exemplar": false, "expr": "label_replace(container_last_seen{environment=\"sila\", name=\"tool-tool-1\"}, \"version\", \"$2\", \"image\", \"(.+):(.+)\")", "instant": true, "legendFormat": "{{version}}", "range": false, "refId": "A"}], "title": "TOOL", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 28, "panels": [], "title": "Website", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "green", "value": 200}, {"color": "red", "value": 404}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 22}, "id": 29, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "probe_http_status_code{environment=\"staging\"}", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Status Code", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-red", "value": 5}, {"color": "orange", "value": 15}, {"color": "green", "value": 30}]}, "unit": "days"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 22}, "id": 33, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "(probe_ssl_earliest_cert_expiry{environment=\"staging\"} - time()) / 86400", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Certificate Validity", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "red"}, {"color": "#EAB839", "value": 99}, {"color": "green", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 4, "y": 22}, "id": 41, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": false}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "avg_over_time(probe_success{environment=\"staging\"}[7d]) * 100", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Availability (7d)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "green", "value": 200}, {"color": "red", "value": 404}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 6, "y": 22}, "id": 30, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "probe_http_status_code{environment=\"tpb\"}", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Status Code", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-red", "value": 5}, {"color": "orange", "value": 15}, {"color": "green", "value": 30}]}, "unit": "days"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 8, "y": 22}, "id": 34, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "(probe_ssl_earliest_cert_expiry{environment=\"tpb\"} - time()) / 86400", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Certificate Validity", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "red"}, {"color": "#EAB839", "value": 99}, {"color": "green", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 10, "y": 22}, "id": 42, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": false}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "avg_over_time(probe_success{environment=\"tpb\"}[7d]) * 100", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Availability (7d)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "green", "value": 200}, {"color": "red", "value": 404}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 12, "y": 22}, "id": 31, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "probe_http_status_code{environment=\"viriyah\"}", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Status Code", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-red", "value": 5}, {"color": "orange", "value": 15}, {"color": "green", "value": 30}]}, "unit": "days"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 14, "y": 22}, "id": 35, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "(probe_ssl_earliest_cert_expiry{environment=\"viriyah\"} - time()) / 86400", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Certificate Validity", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "red"}, {"color": "#EAB839", "value": 99}, {"color": "green", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 16, "y": 22}, "id": 43, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": false}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "avg_over_time(probe_success{environment=\"viriyah\"}[7d]) * 100", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Availability (7d)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "green", "value": 200}, {"color": "red", "value": 400}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 18, "y": 22}, "id": 32, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "probe_http_status_code{environment=\"sila\"}", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Status Code", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "dark-red", "value": 5}, {"color": "orange", "value": 15}, {"color": "green", "value": 30}]}, "unit": "days"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 20, "y": 22}, "id": 36, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "(probe_ssl_earliest_cert_expiry{environment=\"sila\", instance!~\"https://www.salmate-staging.aibrainlab.co\"} - time()) / 86400", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Certificate Validity", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "red"}, {"color": "#EAB839", "value": 99}, {"color": "green", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 22, "y": 22}, "id": 44, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "value", "wideLayout": false}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "avg_over_time(probe_success{environment=\"sila\"}[7d]) * 100", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Availability (7d)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 250}, {"color": "red", "value": 500}, {"color": "dark-red", "value": 1000}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 26}, "id": 19, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{environment=\"staging\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P50", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"staging\"}[5m])) by (le))", "legendFormat": "P95", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{environment=\"staging\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P99", "range": true, "refId": "B"}], "title": "Response Time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 250}, {"color": "red", "value": 500}, {"color": "dark-red", "value": 1000}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 26}, "id": 21, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{environment=\"tpb\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P50", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"tpb\"}[5m])) by (le))", "legendFormat": "P95", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{environment=\"tpb\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P99", "range": true, "refId": "B"}], "title": "Response Time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 250}, {"color": "red", "value": 500}, {"color": "dark-red", "value": 1000}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 26}, "id": 22, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{environment=\"viriyah\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P50", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"viriyah\"}[5m])) by (le))", "legendFormat": "P95", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{environment=\"viriyah\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P99", "range": true, "refId": "B"}], "title": "Response Time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 250}, {"color": "red", "value": 500}, {"color": "dark-red", "value": 1000}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 26}, "id": 20, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{environment=\"sila\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P50", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{environment=\"sila\"}[5m])) by (le))", "legendFormat": "P95", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{environment=\"sila\"}[5m])) by (le))", "hide": false, "instant": false, "legendFormat": "P99", "range": true, "refId": "B"}], "title": "Response Time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 29}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"staging\"}[5m]))", "legendFormat": "Requests", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"staging\", status=~\"4..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 4xx", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"staging\", status=~\"5..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 5xx", "range": true, "refId": "C"}], "title": "Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 29}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"tpb\"}[5m]))", "legendFormat": "Requests", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"tpb\", status=~\"4..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 4xx", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"tpb\", status=~\"5..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 5xx", "range": true, "refId": "C"}], "title": "Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 29}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"viriyah\"}[5m]))", "legendFormat": "Requests", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"viriyah\", status=~\"4..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 4xx", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"viriyah\", status=~\"5..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 5xx", "range": true, "refId": "C"}], "title": "Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 29}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"sila\"}[5m]))", "legendFormat": "Requests", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"sila\", status=~\"4..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 4xx", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{environment=\"sila\", status=~\"5..\"}[5m]))", "hide": false, "instant": false, "legendFormat": "Error HTTP 5xx", "range": true, "refId": "C"}], "title": "Requests", "type": "timeseries"}], "preload": false, "refresh": "30s", "schemaVersion": 41, "tags": ["prometheus", "cadvisor", "node-exporter", "blackbox-exporter", "traefik"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "...Salmate System Health...", "uid": "_aag-system-health_", "version": 48}