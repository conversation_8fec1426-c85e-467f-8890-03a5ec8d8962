{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 6, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 0, "y": 0}, "id": 9, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "max(max_over_time(redis_uptime_in_seconds{environment=\"$environment\"}[$__interval]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 1800}], "title": "Max Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 2, "x": 3, "y": 0}, "hideTimeOverride": true, "id": 12, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(redis_connected_clients{environment=\"$environment\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Clients", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 5, "y": 0}, "hideTimeOverride": true, "id": 11, "maxDataPoints": 100, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(100 * (redis_memory_used_bytes{environment=\"$environment\"}  / redis_memory_max_bytes{environment=\"$environment\"}))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Memory Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(redis_commands_total{environment=\"$environment\"} [1m])) by (cmd)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ cmd }}", "metric": "redis_command_calls_total", "range": true, "refId": "A", "step": 240}], "title": "Total Commands / sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "irate(redis_keyspace_hits_total{environment=\"$environment\"}[5m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "hits, {{ instance }}", "metric": "", "range": true, "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "irate(redis_keyspace_misses_total{environment=\"$environment\"}[5m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "misses, {{ instance }}", "metric": "", "range": true, "refId": "B", "step": 240, "target": ""}], "title": "Hits / Misses per Sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "max"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "redis_memory_used_bytes{environment=\"$environment\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used, {{ instance }}", "metric": "", "range": true, "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "redis_memory_max_bytes{environment=\"$environment\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "max, {{ instance }}", "range": true, "refId": "B", "step": 240}], "title": "Total Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(redis_net_input_bytes_total{environment=\"$environment\"}[5m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ input }}", "range": true, "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(redis_net_output_bytes_total{environment=\"$environment\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ output }}", "range": true, "refId": "B", "step": 240}], "title": "Network I/O", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 70, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "id": 5, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum (redis_db_keys{environment=\"$environment\"}) by (db, environment)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ db }}, {{ instance }}", "range": true, "refId": "A", "step": 240, "target": ""}], "title": "Total Items per DB", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 70, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum (redis_db_keys{environment=\"$environment\"}) by (environment) - sum (redis_db_keys_expiring{environment=\"$environment\"}) by (environment)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "not expiring, {{ instance }}", "range": true, "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum (redis_db_keys_expiring{environment=\"$environment\"}) by (environment)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "expiring, {{ instance }}", "metric": "", "range": true, "refId": "B", "step": 240}], "title": "Expiring vs Not-Expiring Keys", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "evicts"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "memcached_items_evicted_total{instance=\"**********:9150\",job=\"prometheus\"}"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "reclaims"}, "properties": [{"id": "color", "value": {"fixedColor": "#3F6833", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 21}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(redis_expired_keys_total{environment=\"$environment\"}[5m])) by (environment)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "expired, {{ instance }}", "metric": "", "range": true, "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(redis_evicted_keys_total{environment=\"$environment\"}[5m])) by (environment)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "evicted, {{ instance }}", "range": true, "refId": "B", "step": 240}], "title": "Expired/Evicted <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 21}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(redis_connected_clients{environment=\"$environment\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "connected", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(redis_blocked_clients{environment=\"$environment\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "blocked", "range": true, "refId": "B"}], "title": "Connected/Blocked Clients", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 28}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(irate(redis_commands_duration_seconds_total{environment=\"$environment\"}[1m])) by (cmd)\n  /\nsum(irate(redis_commands_total{environment=\"$environment\"}[1m])) by (cmd)\n", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ cmd }}", "metric": "redis_command_calls_total", "range": true, "refId": "A", "step": 240}], "title": "Average Time Spent by Command / sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(irate(redis_commands_duration_seconds_total{environment=\"$environment\"}[1m])) by (cmd) != 0", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ cmd }}", "metric": "redis_command_calls_total", "range": true, "refId": "A", "step": 240}], "title": "Total Time Spent by Command / sec", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": ["prometheus", "redis"], "templating": {"list": [{"allowCustomValue": false, "current": {"text": "staging", "value": "staging"}, "definition": "label_values(environment)", "label": "Environment", "name": "environment", "options": [], "query": {"qryType": 1, "query": "label_values(environment)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "View Redis", "uid": "e008bc3f-81a2-40f9-baf2-a33fd8dec7ec", "version": 9}