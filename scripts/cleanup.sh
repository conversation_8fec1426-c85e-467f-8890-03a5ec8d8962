#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
GRAY='\033[0;90m'
NC='\033[0m' # No Color
GREEN_BG='\033[42m\033[1;37m'
RED_BG='\033[41m\033[1;37m'

# Variables
PARENT_DIR="~/salmate-monitoring"
SCRIPT_DIR="${PARENT_DIR}/scripts}"
PROGRESS_FILE="$PARENT_DIR/.salmate_monitoring_progress"

# Function to print colored output
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to verify command execution
verify_command() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN_BG} SUCCESS ${NC} $1"
        return 0
    else
        echo -e "${RED_BG} FAILED ${NC} $1"
        return 1
    fi
}

# Check if the script is being run from the correct directory
if [[ ! -d "$PARENT_DIR/prometheus" || ! -d "$PARENT_DIR/grafana" ]]; then
    print_message "$RED" "❌ Error: This script must be run from the salmate-monitoring/scripts directory."
    print_message "$YELLOW" "Please run this script as: ./scripts/cleanup.sh"
    exit 1
fi

print_message "$BLUE" "🧹 Starting cleanup of Salmate Monitoring system..."

# Stop and remove containers
print_message "$YELLOW" "🐳 Stopping Docker Compose services..."
cd "$PARENT_DIR" && docker-compose down
verify_command "Stopped monitoring services"

# Remove Docker volumes
print_message "$YELLOW" "🗑️ Removing Docker volumes..."
docker volume rm $(docker volume ls -q | grep -E 'salmatemonitoring_prometheus_data|salmatemonitoring_grafana_data|salmatemonitoring_alertmanager_data') 2>/dev/null
verify_command "Removed Docker volumes"

# Remove progress file
if [ -f "$PROGRESS_FILE" ]; then
    print_message "$YELLOW" "🗑️ Removing progress file..."
    rm "$PROGRESS_FILE"
    verify_command "Removed progress file"
fi

print_message "$GREEN" "✅ Cleanup completed successfully!"
print_message "$BLUE" "🔄 To set up the monitoring system again, run: ./scripts/initial-setup.sh"

exit 0
