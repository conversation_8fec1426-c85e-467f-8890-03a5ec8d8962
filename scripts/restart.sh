#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
GRAY='\033[0;90m'
NC='\033[0m' # No Color
GREEN_BG='\033[42m\033[1;37m'
RED_BG='\033[41m\033[1;37m'

# Variables
PARENT_DIR="~/salmate-monitoring"
SCRIPT_DIR="${PARENT_DIR}/scripts}"

if [ -f "$PARENT_DIR/.env" ]; then
    DOMAIN_NAME=$(grep "^DOMAIN_NAME=" "$PARENT_DIR/.env" | cut -d '=' -f2)
fi

# Function to print colored output
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to verify command execution
verify_command() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN_BG} SUCCESS ${NC} $1"
        return 0
    else
        echo -e "${RED_BG} FAILED ${NC} $1"
        return 1
    fi
}

# Check if the script is being run from the correct directory
if [[ ! -d "$PARENT_DIR/prometheus" || ! -d "$PARENT_DIR/grafana" ]]; then
    print_message "$RED" "❌ Error: This script must be run from the salmate-monitoring/scripts directory."
    print_message "$YELLOW" "Please run this script as: ./scripts/restart.sh [domain_name]"
    exit 1
fi

print_message "$BLUE" "🔄 Restarting Salmate Monitoring with domain: $DOMAIN_NAME"

# Stop the monitoring system
print_message "$YELLOW" "🛑 Stopping monitoring system..."
cd "$PARENT_DIR" && docker-compose down
verify_command "Stopped monitoring system"

# Start the monitoring system
print_message "$YELLOW" "🚀 Starting monitoring system..."
cd "$PARENT_DIR" && docker-compose up -d
verify_command "Started monitoring system"

print_message "$GREEN" "✅ Restart completed successfully!"
print_message "$BLUE" "📊 Monitoring system is now available at:"
print_message "$GREEN" "Traefik Dashboard: https://$DOMAIN_NAME/dashboard/"
print_message "$GREEN" "Prometheus: https://$DOMAIN_NAME/prometheus/"
print_message "$GREEN" "Grafana: https://$DOMAIN_NAME/grafana/"
# Alertmanager temporarily disabled
# print_message "$GREEN" "Alertmanager: https://$DOMAIN_NAME/alertmanager/"
print_message "$YELLOW" "⚠️ Note: It may take a few minutes for SSL certificates to be issued by Let's Encrypt."
print_message "$YELLOW" "⚠️ Make sure your DNS record points to this server and ports 80/443 are open for Let's Encrypt to work."

# Show container status
print_message "$BLUE" "📋 Container status:"
docker ps

# Show logs for troubleshooting
print_message "$BLUE" "📋 Showing Traefik logs for troubleshooting:"
docker logs traefik

# Show file structure for debugging
print_message "$BLUE" "📋 Traefik configuration files:"
ls -la "$PARENT_DIR/traefik/config"
ls -la "$PARENT_DIR/traefik/config/dynamic"

# Check if services are accessible directly
print_message "$BLUE" "📋 Testing direct access to services:"
print_message "$YELLOW" "Prometheus (should return HTTP 200):"
curl -s -o /dev/null -w "%{http_code}" http://localhost:9090
echo ""
print_message "$YELLOW" "Grafana (should return HTTP 200 or 302):"
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000
echo ""

print_message "$BLUE" "📋 For direct access (bypassing Traefik), try:"
print_message "$GRAY" "Prometheus: http://localhost:9090"
print_message "$GRAY" "Grafana: http://localhost:3000"

print_message "$BLUE" "📋 To see live logs, run:"
print_message "$GRAY" "docker logs -f traefik"
print_message "$GRAY" "docker logs -f prometheus"
print_message "$GRAY" "docker logs -f grafana"

exit 0
